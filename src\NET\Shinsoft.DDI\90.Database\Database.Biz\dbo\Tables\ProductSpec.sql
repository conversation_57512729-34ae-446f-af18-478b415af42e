CREATE TABLE [dbo].[ProductSpec] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [Default_ProductSpec_ID] DEFAULT (NEWSEQUENTIALID()),
    [ProductId]                 UNIQUEIDENTIFIER            NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Spec]                      NVARCHAR(50)                NOT NULL,
    Unit                        NVARCHAR(50)                NULL,
    [DosageFormId]              UNIQUEIDENTIFIER            NULL,
    [PharmaceuticalFactory]     NVARCHAR(200)               NULL,
    MaterialGroup               NVARCHAR(50)                NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL,
    [CreateTime]                DATETIME                    NULL,
    [LastEditor]                NVARCHAR(50)                NULL,
    [LastEditTime]              DATETIME                    NULL,
    CONSTRAINT [PK_ProductSpec] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ProductSpec_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ID]),
    CONSTRAINT [FK_ProductSpec_DosageForm] FOREIGN KEY ([DosageFormId]) REFERENCES [dbo].[Dict] ([ID])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品规格',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'ProductId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'规格编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'规格',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'Spec'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'单位',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'Unit'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'剂型ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'DosageFormId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'生产厂家',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'PharmaceuticalFactory'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'分型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'MaterialGroup'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
