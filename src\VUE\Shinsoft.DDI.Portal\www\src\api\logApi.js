import axios from '@/utils/axios'

/**
 * 货主相关API
 */
export const logApi = {
  /**
   * 查询货主列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.receiverCode 客户编码
   * @param {string} params.receiverName 客户名称
   * @param {datetiem} params.logTime 日志时间
   * @param {string} params.message 日志内容
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryReceiverClientLog(params) {
    return axios.get('/Log/QueryReceiverClientLog', { params })
  },

}

export default logApi